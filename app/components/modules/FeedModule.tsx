import { useEffect, useState, useCallback, useRef } from "react";
import { type EnrichedActivity, type EnrichedReaction } from "getstream";
import { useNavigate } from "react-router";
import {
  Loader2,
  MessageSquare,
  Heart,
  Share2,
  MoreHorizontal,
  Send,
  ImageIcon,
  X,
} from "lucide-react";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

import type { FeedModule as FeedModuleType } from "~/lib/api/types";
import { useAppContext } from "~/lib/providers/app-context";
import { useProfile, useGetUploadUrl } from "~/lib/api/client-queries";
import { canUserPostToFeed } from "~/lib/utils/feed";

// Helper function to check if a file is an image
const isImageFile = (file: File): boolean => {
  return file.type.startsWith("image/");
};

interface FeedModuleProps {
  module: FeedModuleType;
  params: {
    groupId: string;
    cohortId: string;
    moduleId: string;
  };
}

interface EnrichedActivityWithText extends EnrichedActivity {
  text?: string;
  message?: string;
  image?: string;
  attachments?: Array<{
    type: string;
    image_url?: string;
    asset_url?: string;
    custom?: Record<string, any>;
  }>;
  own_reactions?: {
    like?: EnrichedReaction[];
    comment?: EnrichedReaction[];
  };
  reaction_counts?: {
    like?: number;
    comment?: number;
  };
}

type CommentWithUser = EnrichedReaction & {
  user?: {
    id: string;
    created_at: string;
    updated_at: string;
    data: {
      name?: string;
      image?: string;
    };
  };
};

export function FeedModule({ module, params }: FeedModuleProps) {
  const { userId, streamClient } = useAppContext();
  const { data: profileData } = useProfile();
  const getUploadUrl = useGetUploadUrl();
  const navigate = useNavigate();
  const [activities, setActivities] = useState<EnrichedActivityWithText[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedComments, setExpandedComments] = useState<Set<string>>(
    new Set()
  );
  const [commentInputs, setCommentInputs] = useState<Record<string, string>>(
    {}
  );
  const [loadingComments, setLoadingComments] = useState<
    Record<string, boolean>
  >({});
  const [activityComments, setActivityComments] = useState<
    Record<string, CommentWithUser[]>
  >({});
  const [postText, setPostText] = useState("");
  const [postImage, setPostImage] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isPosting, setIsPosting] = useState(false);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [nextPageId, setNextPageId] = useState<string | null>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [shareDropdownOpen, setShareDropdownOpen] = useState<string | null>(
    null
  );

  // Check if user has permission to post
  const userRole = profileData?.data?.role;
  const userOrgId = profileData?.data?.organizationId;
  const canPost = canUserPostToFeed(userRole, userOrgId, module.config.feedId);

  // Fetch feed when client is ready
  useEffect(() => {
    const initializeFeed = async () => {
      if (!streamClient) return;

      try {
        setLoading(true);
        setError(null);

        // Get the feed using the config from the module
        const feed = streamClient.feed(
          module.config.feedGroup,
          module.config.feedId
        );

        // Fetch activities with enrichment
        const response = await feed.get({
          limit: 25,
          withReactionCounts: true,
          withOwnReactions: true,
          enrich: true, // This is the key parameter for enrichment
        });

        console.log("Activities", response);

        setActivities(response.results as EnrichedActivityWithText[]);

        // Set up pagination
        const hasMore = response.results.length === 25;
        setHasNextPage(hasMore);
        if (hasMore && response.results.length > 0) {
          const lastActivity = response.results[response.results.length - 1];
          setNextPageId(lastActivity.id);
        } else {
          setNextPageId(null);
        }
      } catch (err) {
        console.error("Error initializing feed:", err);
        setError("Failed to load feed. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    initializeFeed();
  }, [streamClient, module.config]);

  const loadMoreActivities = useCallback(async () => {
    if (!streamClient || !nextPageId || isLoadingMore || !hasNextPage) {
      return;
    }

    setIsLoadingMore(true);
    try {
      const feed = streamClient.feed(
        module.config.feedGroup,
        module.config.feedId
      );

      // Fetch next page using id_lt for pagination
      const response = await feed.get({
        limit: 25,
        id_lt: nextPageId,
        withReactionCounts: true,
        withOwnReactions: true,
        enrich: true,
      });

      // Append new activities to existing ones
      setActivities((prev) => [
        ...prev,
        ...(response.results as EnrichedActivityWithText[]),
      ]);

      // Update pagination state
      const hasMore = response.results.length === 25;
      setHasNextPage(hasMore);
      if (hasMore && response.results.length > 0) {
        const lastActivity = response.results[response.results.length - 1];
        setNextPageId(lastActivity.id);
      } else {
        setNextPageId(null);
      }
    } catch (err) {
      console.error("Error loading more activities:", err);
      setError("Failed to load more activities. Please try again.");
    } finally {
      setIsLoadingMore(false);
    }
  }, [streamClient, nextPageId, isLoadingMore, hasNextPage, module.config]);

  // Infinite scroll effect
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
      const distanceFromBottom = scrollHeight - (scrollTop + clientHeight);

      // Trigger load more when user is within 200px of the bottom
      if (distanceFromBottom <= 200 && hasNextPage && !isLoadingMore) {
        loadMoreActivities();
      }
    };

    // Add scroll event listener to the container
    scrollContainer.addEventListener("scroll", handleScroll, { passive: true });

    // Cleanup
    return () => {
      scrollContainer.removeEventListener("scroll", handleScroll);
    };
  }, [loadMoreActivities, hasNextPage, isLoadingMore]);

  // Close share dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setShareDropdownOpen(null);
    };

    if (shareDropdownOpen) {
      document.addEventListener("click", handleClickOutside);
      return () => {
        document.removeEventListener("click", handleClickOutside);
      };
    }
  }, [shareDropdownOpen]);

  const handleLike = async (activityId: string) => {
    if (!streamClient) return;

    try {
      const reaction = await streamClient.reactions.add("like", activityId);

      // Update the activities state with the new reaction
      setActivities((prevActivities) =>
        prevActivities.map((activity) => {
          if (activity.id === activityId) {
            return {
              ...activity,
              own_reactions: {
                ...activity.own_reactions,
                like: [...(activity.own_reactions?.like || []), reaction],
              },
              reaction_counts: {
                ...activity.reaction_counts,
                like: (activity.reaction_counts?.like || 0) + 1,
              },
            };
          }
          return activity;
        })
      );
    } catch (error) {
      console.error("Error adding like:", error);
    }
  };

  const handleUnlike = async (activityId: string, reactionId: string) => {
    if (!streamClient) return;

    try {
      await streamClient.reactions.delete(reactionId);

      // Update the activities state by removing the reaction
      setActivities((prevActivities) =>
        prevActivities.map((activity) => {
          if (activity.id === activityId) {
            return {
              ...activity,
              own_reactions: {
                ...activity.own_reactions,
                like: activity.own_reactions?.like?.filter(
                  (r) => r.id !== reactionId
                ),
              },
              reaction_counts: {
                ...activity.reaction_counts,
                like: Math.max((activity.reaction_counts?.like || 0) - 1, 0),
              },
            };
          }
          return activity;
        })
      );
    } catch (error) {
      console.error("Error removing like:", error);
    }
  };

  const toggleComments = async (activityId: string) => {
    const newExpanded = new Set(expandedComments);

    if (newExpanded.has(activityId)) {
      newExpanded.delete(activityId);
    } else {
      newExpanded.add(activityId);

      // Fetch comments if not already loaded
      if (!activityComments[activityId] && streamClient) {
        setLoadingComments({ ...loadingComments, [activityId]: true });
        try {
          const response = await streamClient.reactions.filter({
            activity_id: activityId,
            kind: "comment",
            limit: 50,
            // Optional: Add ordering for consistent results
            // id_lte: lastCommentId, // for pagination if needed
          });

          console.log("Comments", response);

          setActivityComments({
            ...activityComments,
            [activityId]: response.results as CommentWithUser[],
          });
        } catch (error) {
          console.error("Error fetching comments:", error);
        } finally {
          setLoadingComments({ ...loadingComments, [activityId]: false });
        }
      }
    }

    setExpandedComments(newExpanded);
  };

  const handleAddComment = async (activityId: string) => {
    if (!streamClient || !commentInputs[activityId]?.trim()) return;

    try {
      const comment = await streamClient.reactions.add("comment", activityId, {
        text: commentInputs[activityId],
      });

      // Add comment to local state with proper user data structure
      const newComment: CommentWithUser = {
        ...comment,
        user: {
          id: userId!,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          data: {
            name:
              profileData?.data?.firstName && profileData?.data?.lastName
                ? `${profileData.data.firstName} ${profileData.data.lastName}`.trim()
                : profileData?.data?.firstName ||
                  profileData?.data?.lastName ||
                  "Anonymous",
          },
        },
      };

      console.log("New Comment", newComment);

      setActivityComments({
        ...activityComments,
        [activityId]: [newComment, ...(activityComments[activityId] || [])],
      });

      // Update activity comment count
      setActivities((prevActivities) =>
        prevActivities.map((activity) => {
          if (activity.id === activityId) {
            return {
              ...activity,
              reaction_counts: {
                ...activity.reaction_counts,
                comment: (activity.reaction_counts?.comment || 0) + 1,
              },
            };
          }
          return activity;
        })
      );

      // Clear input
      setCommentInputs({ ...commentInputs, [activityId]: "" });
    } catch (error) {
      console.error("Error adding comment:", error);
    }
  };

  const handleCreatePost = async () => {
    if (!streamClient || !postText.trim()) return;

    setIsPosting(true);
    try {
      const userFeed = streamClient.feed(
        module.config.feedGroup,
        module.config.feedId
      );

      // Create activity data
      const activityData: any = {
        verb: "post",
        message: postText,
        object: `cohort:${module.config.feedId}`,
        // Add the target feed to specify where this should appear
        to: [`${module.config.feedGroup}:${module.config.feedId}`],
        time: new Date().toISOString(),
      };

      // Handle image upload if there's a selected file
      if (selectedFile) {
        try {
          // Get upload URL from backend
          const uploadUrlResponse = await getUploadUrl.mutateAsync({
            contentType: selectedFile.type,
            fileName: selectedFile.name,
          });

          // Upload file to the presigned URL
          const uploadResponse = await fetch(uploadUrlResponse.uploadUrl, {
            method: "PUT",
            headers: {
              "Content-Type": selectedFile.type,
            },
            body: selectedFile,
          });

          if (!uploadResponse.ok) {
            throw new Error(`Upload failed: ${uploadResponse.statusText}`);
          }

          // Add attachment to activity using the CDN URL
          activityData.attachments = [
            {
              type: isImageFile(selectedFile) ? "image" : "file",
              [isImageFile(selectedFile) ? "image_url" : "asset_url"]:
                uploadUrlResponse.cdnUrl,
              custom: {},
            },
          ];
        } catch (uploadError) {
          console.error("Error uploading file:", uploadError);
          setError("Failed to upload image. Please try again.");
          return;
        }
      }

      // Add the activity to the user's feed
      await userFeed.addActivity(activityData);

      // Re-fetch the feed to get the enriched data including the new post
      const response = await userFeed.get({
        limit: 25,
        withReactionCounts: true,
        withOwnReactions: true,
        enrich: true,
      });

      // Update activities with the fresh enriched data
      setActivities(response.results as EnrichedActivityWithText[]);

      // Reset pagination after adding new post
      const hasMore = response.results.length === 25;
      setHasNextPage(hasMore);
      if (hasMore && response.results.length > 0) {
        const lastActivity = response.results[response.results.length - 1];
        setNextPageId(lastActivity.id);
      } else {
        setNextPageId(null);
      }

      // Clear the form
      setPostText("");
      setPostImage(null);
      setSelectedFile(null);
    } catch (error) {
      console.error("Error creating post:", error);
      setError("Failed to create post. Please try again.");
    } finally {
      setIsPosting(false);
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Clear any existing errors
      setError(null);

      // Validate file type and size
      if (!isImageFile(file)) {
        setError("Please select a valid image file.");
        return;
      }

      // Check file size (max 100MB as per GetStream docs)
      const maxSize = 100 * 1024 * 1024; // 100MB in bytes
      if (file.size > maxSize) {
        setError("Image file size must be less than 100MB.");
        return;
      }

      // Store the file for upload and create preview
      setSelectedFile(file);

      // Create a preview using FileReader
      const reader = new FileReader();
      reader.onloadend = () => {
        setPostImage(reader.result as string);
      };
      reader.readAsDataURL(file);
    }

    // Reset the input value so the same file can be selected again if needed
    e.target.value = "";
  };

  // Initialize dayjs with plugins
  dayjs.extend(relativeTime);
  dayjs.extend(utc);
  dayjs.extend(timezone);

  const formatTime = (timestamp: string) => {
    // Parse the UTC timestamp and convert to local timezone
    const date = dayjs.utc(timestamp).local();
    const now = dayjs();
    const diffInDays = now.diff(date, "day");

    // For dates older than 7 days, show formatted date
    if (diffInDays > 7) {
      return date.format("MMM D, YYYY");
    }

    // Use dayjs humanize for recent dates
    return date.fromNow();
  };

  const getActorName = (actor: any): string => {
    if (typeof actor === "string") return actor;
    if (actor?.data?.name) return actor.data.name;
    if (actor?.id) return actor.id;
    return "Unknown User";
  };

  const getActorInitial = (actor: any): string => {
    const name = getActorName(actor);
    return name[0]?.toUpperCase() || "U";
  };

  const getActorImage = (actor: any): string | null => {
    if (actor?.data?.image) return actor.data.image;
    return null;
  };

  const getActivityContent = (activity: EnrichedActivityWithText): string => {
    if (activity.message) return activity.message;
    if (activity.text) return activity.text;
    if (typeof activity.object === "string") return activity.object;
    const obj = activity.object as any;
    if (obj?.text) return obj.text;
    if (obj?.content) return obj.content;
    if (obj?.id) return `${activity.verb} ${obj.id}`;
    return `${activity.verb}`;
  };

  const handlePostClick = (activityId: string) => {
    navigate(
      `/groups/${params.groupId}/cohorts/${params.cohortId}/modules/${params.moduleId}/posts/${activityId}`
    );
  };

  const handleShare = async (activityId: string) => {
    const postUrl = `${window.location.origin}/groups/${params.groupId}/cohorts/${params.cohortId}/modules/${params.moduleId}/posts/${activityId}`;

    try {
      await navigator.clipboard.writeText(postUrl);
      // TODO: Show success toast/notification
      console.log("Link copied to clipboard!");
    } catch (err) {
      // Fallback for older browsers
      try {
        const textArea = document.createElement("textarea");
        textArea.value = postUrl;
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand("copy");
        document.body.removeChild(textArea);
        console.log("Link copied to clipboard (fallback)!");
      } catch (fallbackErr) {
        console.error("Failed to copy link:", fallbackErr);
        // TODO: Show error toast/notification
      }
    }
  };

  return (
    <div className="flex flex-col h-full bg-black">
      {/* Top Bar - Consistent with other modules */}
      <div className="sticky top-0 z-50 bg-black/10 backdrop-blur-md border-b border-zinc-900">
        <div className="max-w-4xl mx-auto px-8 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-semibold text-white">
                {module.name}
              </h1>
              <p className="text-sm text-zinc-400">
                {activities.length} Activities
              </p>
            </div>
            <button className="p-2 hover:bg-zinc-900 rounded-lg transition-colors">
              <MoreHorizontal className="w-5 h-5 text-zinc-400" />
            </button>
          </div>
        </div>
      </div>

      {/* Feed Content */}
      <div ref={scrollContainerRef} className="flex-1 overflow-auto bg-black">
        <div className="max-w-4xl mx-auto px-8 py-12">
          {/* Create Post Form */}
          {!loading && !error && canPost && (
            <div className="bg-zinc-900 rounded-lg shadow-sm border border-zinc-700 p-6 mb-6">
              <h3 className="text-lg font-semibold text-white mb-4">
                Create a Post
              </h3>

              <textarea
                value={postText}
                onChange={(e) => setPostText(e.target.value)}
                placeholder="What's on your mind?"
                className="w-full px-4 py-3 text-white bg-zinc-700 border border-zinc-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none placeholder-zinc-400"
                rows={3}
              />

              {postImage && (
                <div className="mt-3 relative">
                  <img
                    src={postImage}
                    alt="Upload preview"
                    className="max-h-64 rounded-lg"
                  />
                  <button
                    onClick={() => {
                      setPostImage(null);
                      setSelectedFile(null);
                    }}
                    className="absolute top-2 right-2 p-1 bg-black/50 rounded-full hover:bg-black/70 transition-colors"
                  >
                    <X className="w-4 h-4 text-white" />
                  </button>
                </div>
              )}

              <div className="mt-4 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <label className="cursor-pointer">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                    />
                    <div className="p-2 text-zinc-400 hover:bg-zinc-700 rounded-lg transition-colors">
                      <ImageIcon className="w-5 h-5" />
                    </div>
                  </label>
                </div>

                <button
                  onClick={handleCreatePost}
                  disabled={!postText.trim() || isPosting}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
                >
                  {isPosting ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      {selectedFile ? "Uploading & Posting..." : "Posting..."}
                    </>
                  ) : (
                    "Post"
                  )}
                </button>
              </div>
            </div>
          )}

          {loading && (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
            </div>
          )}

          {error && (
            <div className="bg-red-900/50 border border-red-700 rounded-lg p-4 text-red-300">
              {error}
            </div>
          )}

          {!loading && !error && activities.length === 0 && (
            <div className="text-center py-12">
              <p className="text-zinc-400">
                No activities yet. Check back later!
              </p>
            </div>
          )}

          {!loading && !error && activities.length > 0 && (
            <div className="space-y-4">
              {activities.map((activity) => (
                <div
                  key={activity.id}
                  className="bg-zinc-900 rounded-lg shadow-sm border border-zinc-700 p-6"
                >
                  {/* Activity Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      {getActorImage(activity.actor) ? (
                        <img
                          src={getActorImage(activity.actor)!}
                          alt={getActorName(activity.actor)}
                          className="w-10 h-10 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 font-semibold">
                            {getActorInitial(activity.actor)}
                          </span>
                        </div>
                      )}
                      <div>
                        <p className="font-medium text-white">
                          {getActorName(activity.actor)}
                        </p>
                        <p className="text-sm text-zinc-400">
                          {formatTime(activity.time)}
                        </p>
                      </div>
                    </div>
                    <button className="text-zinc-400 hover:text-zinc-300">
                      <MoreHorizontal className="w-5 h-5" />
                    </button>
                  </div>

                  {/* Activity Content */}
                  <div
                    className="mb-4 cursor-pointer hover:bg-zinc-800/50 rounded-lg p-2 -m-2 transition-colors"
                    onClick={() => handlePostClick(activity.id)}
                  >
                    <p className="text-zinc-200">
                      {getActivityContent(activity)}
                    </p>

                    {/* Display legacy image field */}
                    {activity.image && (
                      <img
                        src={activity.image}
                        alt="Activity image"
                        className="mt-3 rounded-lg max-w-full"
                      />
                    )}

                    {/* Display attachments */}
                    {activity.attachments &&
                      activity.attachments.length > 0 && (
                        <div className="mt-3 space-y-2">
                          {activity.attachments.map((attachment, index) => (
                            <div key={index}>
                              {attachment.type === "image" &&
                                attachment.image_url && (
                                  <img
                                    src={attachment.image_url}
                                    alt="Attached image"
                                    className="rounded-lg max-w-full"
                                  />
                                )}
                              {attachment.type === "file" &&
                                attachment.asset_url && (
                                  <a
                                    href={attachment.asset_url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="inline-flex items-center px-3 py-2 bg-zinc-100 rounded-lg hover:bg-zinc-200 transition-colors"
                                    onClick={(e) => e.stopPropagation()}
                                  >
                                    <span className="text-sm text-zinc-700">
                                      📎 View File
                                    </span>
                                  </a>
                                )}
                            </div>
                          ))}
                        </div>
                      )}
                  </div>

                  {/* Activity Actions */}
                  <div className="flex items-center gap-6 pt-4 border-t border-zinc-700">
                    {activity.own_reactions?.like?.length ? (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleUnlike(
                            activity.id,
                            activity.own_reactions!.like![0].id
                          );
                        }}
                        className="flex items-center gap-2 text-blue-400 hover:text-blue-300 transition-colors"
                      >
                        <Heart className="w-5 h-5 fill-current" />
                        <span className="text-sm">
                          {activity.reaction_counts?.like || 0}
                        </span>
                      </button>
                    ) : (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleLike(activity.id);
                        }}
                        className="flex items-center gap-2 text-zinc-400 hover:text-blue-400 transition-colors"
                      >
                        <Heart className="w-5 h-5" />
                        <span className="text-sm">
                          {activity.reaction_counts?.like || 0}
                        </span>
                      </button>
                    )}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleComments(activity.id);
                      }}
                      className="flex items-center gap-2 text-zinc-400 hover:text-blue-400 transition-colors"
                    >
                      <MessageSquare className="w-5 h-5" />
                      <span className="text-sm">
                        {activity.reaction_counts?.comment || 0}
                      </span>
                    </button>
                    <div className="relative">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setShareDropdownOpen(
                            shareDropdownOpen === activity.id
                              ? null
                              : activity.id
                          );
                        }}
                        className="flex items-center gap-2 text-zinc-400 hover:text-blue-400 transition-colors"
                      >
                        <Share2 className="w-5 h-5" />
                        <span className="text-sm">Share</span>
                      </button>

                      {shareDropdownOpen === activity.id && (
                        <div className="absolute bottom-full right-0 mb-2 bg-zinc-800 border border-zinc-700 rounded-lg shadow-lg py-1 min-w-[120px] z-10">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleShare(activity.id);
                              setShareDropdownOpen(null);
                            }}
                            className="w-full px-3 py-2 text-left text-sm text-zinc-200 hover:bg-zinc-700 transition-colors"
                          >
                            Copy Link
                          </button>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Comments Section */}
                  {expandedComments.has(activity.id) && (
                    <div
                      className="mt-4 pt-4 border-t border-zinc-700"
                      onClick={(e) => e.stopPropagation()}
                    >
                      {/* Comment Input */}
                      <div className="flex gap-2 mb-4">
                        <input
                          type="text"
                          value={commentInputs[activity.id] || ""}
                          onChange={(e) =>
                            setCommentInputs({
                              ...commentInputs,
                              [activity.id]: e.target.value,
                            })
                          }
                          onKeyDown={(e) => {
                            if (e.key === "Enter") {
                              handleAddComment(activity.id);
                            }
                          }}
                          placeholder="Write a comment..."
                          className="flex-1 px-3 py-2 text-white bg-zinc-700 border border-zinc-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder-zinc-400"
                        />
                        <button
                          onClick={() => handleAddComment(activity.id)}
                          disabled={!commentInputs[activity.id]?.trim()}
                          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        >
                          <Send className="w-4 h-4" />
                        </button>
                      </div>

                      {/* Comments List */}
                      {loadingComments[activity.id] ? (
                        <div className="flex justify-center py-4">
                          <Loader2 className="w-5 h-5 animate-spin text-blue-600" />
                        </div>
                      ) : (
                        <div className="space-y-3">
                          {activityComments[activity.id]?.map((comment) => (
                            <div key={comment.id} className="flex gap-3">
                              {comment.user?.data?.image ? (
                                <img
                                  src={comment.user.data.image}
                                  alt={comment.user.data.name || "User"}
                                  className="w-8 h-8 rounded-full object-cover"
                                />
                              ) : (
                                <div className="w-8 h-8 bg-zinc-600 rounded-full flex items-center justify-center">
                                  <span className="text-zinc-300 text-sm font-medium">
                                    {(comment.user?.data?.name ||
                                      "U")[0].toUpperCase()}
                                  </span>
                                </div>
                              )}
                              <div className="flex-1">
                                <div className="bg-zinc-700 rounded-lg px-3 py-2">
                                  <p className="text-sm font-medium text-white">
                                    {comment.user?.data?.name || "Anonymous"}
                                  </p>
                                  <p className="text-sm text-zinc-300">
                                    {(comment.data as any)?.text ||
                                      comment.data}
                                  </p>
                                </div>
                                <p className="text-xs text-zinc-500 mt-1">
                                  {formatTime(comment.created_at)}
                                </p>
                              </div>
                            </div>
                          )) || []}
                          {(!activityComments[activity.id] ||
                            activityComments[activity.id].length === 0) && (
                            <p className="text-sm text-zinc-400 text-center py-2">
                              No comments yet. Be the first to comment!
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {/* Debug: Manual Load More Button */}
          {!loading &&
            !error &&
            activities.length > 0 &&
            hasNextPage &&
            !isLoadingMore && (
              <div className="flex justify-center py-4">
                <button
                  onClick={loadMoreActivities}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  Manual Load More (Debug)
                </button>
              </div>
            )}

          {/* Infinite Scroll Loading Indicator */}
          {!loading && !error && activities.length > 0 && isLoadingMore && (
            <div className="flex justify-center py-8">
              <div className="flex items-center gap-2 text-zinc-400">
                <Loader2 className="w-5 h-5 animate-spin" />
                <span>Loading more activities...</span>
              </div>
            </div>
          )}

          {/* End of feed indicator */}
          {!loading &&
            !error &&
            activities.length > 0 &&
            !hasNextPage &&
            !isLoadingMore && (
              <div className="flex justify-center py-8">
                <div className="text-zinc-500 text-sm">
                  You've reached the end of the feed
                </div>
              </div>
            )}
        </div>
      </div>
    </div>
  );
}
